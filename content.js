// 人机验证自动处理器 Content Script
console.log('[人机验证处理器] 扩展已加载');

// 配置参数
const CONFIG = {
    // 目标URL模式
    TARGET_URL_PATTERN: 'https://signup.live.com/signup',
    
    // 检测人机验证页面的选择器和文本
    VERIFICATION_SELECTOR: '#view > div > div:nth-child(2) > div > h1',
    VERIFICATION_TEXT: '证明你不是机器人',
    
    // 需要点击的元素选择器
    FIRST_CLICK_SELECTOR: '#xpplOSafccGevzl',
    SECOND_CLICK_SELECTOR: '#WKEHFCDuGlNZPmJ',
    
    // 等待时间（毫秒）
    WAIT_TIME: 12000, // 12秒
    
    // 检查间隔（毫秒）
    CHECK_INTERVAL: 1000, // 1秒
    
    // 最大等待时间（毫秒）
    MAX_WAIT_TIME: 30000 // 30秒
};

// 全局状态
let isProcessing = false;
let checkCount = 0;

/**
 * 等待指定时间
 * @param {number} ms 等待时间（毫秒）
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 安全地获取元素
 * @param {string} selector CSS选择器
 * @returns {Element|null} 找到的元素或null
 */
function safeQuerySelector(selector) {
    try {
        return document.querySelector(selector);
    } catch (error) {
        console.error(`[人机验证处理器] 选择器错误: ${selector}`, error);
        return null;
    }
}

/**
 * 安全地点击元素
 * @param {string} selector CSS选择器
 * @returns {boolean} 是否成功点击
 */
function safeClick(selector) {
    try {
        const element = safeQuerySelector(selector);
        if (element) {
            element.click();
            console.log(`[人机验证处理器] 成功点击元素: ${selector}`);
            return true;
        } else {
            console.warn(`[人机验证处理器] 未找到元素: ${selector}`);
            return false;
        }
    } catch (error) {
        console.error(`[人机验证处理器] 点击元素失败: ${selector}`, error);
        return false;
    }
}

/**
 * 检测是否进入人机验证页面
 * @returns {boolean} 是否检测到人机验证页面
 */
function detectVerificationPage() {
    const element = safeQuerySelector(CONFIG.VERIFICATION_SELECTOR);
    if (element) {
        const text = element.textContent.trim();
        console.log(`[人机验证处理器] 检测到元素文本: "${text}"`);
        
        if (text === CONFIG.VERIFICATION_TEXT) {
            console.log('[人机验证处理器] ✅ 确认进入人机验证页面');
            return true;
        }
    }
    return false;
}

/**
 * 执行人机验证自动处理流程
 */
async function processVerification() {
    if (isProcessing) {
        console.log('[人机验证处理器] 已在处理中，跳过');
        return;
    }
    
    isProcessing = true;
    console.log('[人机验证处理器] 🚀 开始执行人机验证处理流程');
    
    try {
        // 步骤1: 点击第一个元素
        console.log('[人机验证处理器] 步骤1: 点击第一个元素');
        const firstClickSuccess = safeClick(CONFIG.FIRST_CLICK_SELECTOR);
        
        if (!firstClickSuccess) {
            console.error('[人机验证处理器] ❌ 第一个元素点击失败');
            return;
        }
        
        // 步骤2: 等待12秒
        console.log(`[人机验证处理器] 步骤2: 等待 ${CONFIG.WAIT_TIME / 1000} 秒...`);
        await sleep(CONFIG.WAIT_TIME);
        
        // 步骤3: 点击第二个元素
        console.log('[人机验证处理器] 步骤3: 点击第二个元素');
        const secondClickSuccess = safeClick(CONFIG.SECOND_CLICK_SELECTOR);
        
        if (secondClickSuccess) {
            console.log('[人机验证处理器] ✅ 人机验证处理流程完成');
        } else {
            console.error('[人机验证处理器] ❌ 第二个元素点击失败');
        }
        
    } catch (error) {
        console.error('[人机验证处理器] ❌ 处理流程出错:', error);
    } finally {
        isProcessing = false;
    }
}

/**
 * 定期检查是否需要处理人机验证
 */
function startMonitoring() {
    console.log('[人机验证处理器] 开始监控页面...');
    
    const checkInterval = setInterval(() => {
        checkCount++;
        
        // 检查是否超过最大等待时间
        if (checkCount * CONFIG.CHECK_INTERVAL > CONFIG.MAX_WAIT_TIME) {
            console.log('[人机验证处理器] 监控超时，停止检查');
            clearInterval(checkInterval);
            return;
        }
        
        // 检查当前URL是否匹配
        if (!window.location.href.includes(CONFIG.TARGET_URL_PATTERN)) {
            console.log('[人机验证处理器] URL不匹配，停止监控');
            clearInterval(checkInterval);
            return;
        }
        
        // 检测人机验证页面
        if (detectVerificationPage()) {
            clearInterval(checkInterval);
            processVerification();
        }
        
    }, CONFIG.CHECK_INTERVAL);
}

// 页面加载完成后开始监控
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startMonitoring);
} else {
    startMonitoring();
}

// 监听页面变化（SPA应用可能需要）
const observer = new MutationObserver((mutations) => {
    if (!isProcessing && detectVerificationPage()) {
        processVerification();
    }
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});

console.log('[人机验证处理器] 初始化完成');
