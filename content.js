// 人机验证自动处理器 Content Script
console.log('[人机验证处理器] 扩展已加载');

// 配置参数
const CONFIG = {
    // 目标URL模式
    TARGET_URL_PATTERN: 'https://signup.live.com/signup',

    // 检测人机验证页面的选择器和文本
    VERIFICATION_SELECTOR: '#view > div > div:nth-child(2) > div > h1',
    VERIFICATION_TEXT: '证明你不是机器人',

    // 需要点击的元素选择器（使用结构化选择器，因为ID会变化）
    // 第一个元素：可访问性挑战按钮（通过aria-label和结构定位）
    FIRST_CLICK_SELECTOR: 'a[role="button"][aria-label="可访问性挑战"]',
    // 备用选择器：通过SVG和文本内容定位
    FIRST_CLICK_BACKUP_SELECTOR: 'a[tabindex="0"]:has(svg):has(div:contains("可访问性挑战"))',

    // 第二个元素：按住按钮（通过aria-label和class定位）
    SECOND_CLICK_SELECTOR: 'div[role="button"][aria-label*="按住"][aria-label*="人工挑战"]',
    // 备用选择器：通过class和文本内容定位
    SECOND_CLICK_BACKUP_SELECTOR: '.tiZTkgevcrkBKyp[role="button"]',

    // 等待时间（毫秒）
    WAIT_TIME: 12000, // 12秒

    // 检查间隔（毫秒）
    CHECK_INTERVAL: 1000, // 1秒

    // 最大等待时间（毫秒）
    MAX_WAIT_TIME: 30000 // 30秒
};

// 全局状态
let isProcessing = false;
let checkCount = 0;

/**
 * 等待指定时间
 * @param {number} ms 等待时间（毫秒）
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 安全地获取元素（支持多个选择器）
 * @param {string|string[]} selectors CSS选择器或选择器数组
 * @returns {Element|null} 找到的元素或null
 */
function safeQuerySelector(selectors) {
    const selectorArray = Array.isArray(selectors) ? selectors : [selectors];

    for (const selector of selectorArray) {
        try {
            // 处理包含:has()的选择器（需要特殊处理）
            if (selector.includes(':has(') || selector.includes(':contains(')) {
                const element = findElementByStructure(selector);
                if (element) return element;
            } else {
                const element = document.querySelector(selector);
                if (element) return element;
            }
        } catch (error) {
            console.warn(`[人机验证处理器] 选择器 "${selector}" 执行失败:`, error);
        }
    }

    console.warn(`[人机验证处理器] 所有选择器都未找到元素:`, selectorArray);
    return null;
}

/**
 * 通过结构特征查找元素（用于复杂选择器）
 * @param {string} description 元素描述
 * @returns {Element|null} 找到的元素或null
 */
function findElementByStructure(description) {
    try {
        // 查找可访问性挑战按钮
        if (description.includes('可访问性挑战')) {
            const buttons = document.querySelectorAll('a[role="button"]');
            for (const button of buttons) {
                const ariaLabel = button.getAttribute('aria-label');
                const hasDiv = button.querySelector('div');
                const hasSvg = button.querySelector('svg');

                if ((ariaLabel && ariaLabel.includes('可访问性挑战')) ||
                    (hasDiv && hasDiv.textContent.includes('可访问性挑战')) ||
                    (hasSvg && hasDiv)) {
                    return button;
                }
            }
        }

        // 查找按住按钮
        if (description.includes('按住') || description.includes('人工挑战')) {
            const buttons = document.querySelectorAll('div[role="button"]');
            for (const button of buttons) {
                const ariaLabel = button.getAttribute('aria-label');
                const textContent = button.textContent;

                if ((ariaLabel && (ariaLabel.includes('按住') || ariaLabel.includes('人工挑战'))) ||
                    (textContent && textContent.includes('按住'))) {
                    return button;
                }
            }
        }

        return null;
    } catch (error) {
        console.error(`[人机验证处理器] 结构查找失败:`, error);
        return null;
    }
}

/**
 * 安全地点击元素
 * @param {string|string[]} selectors CSS选择器或选择器数组
 * @param {string} elementName 元素名称（用于日志）
 * @returns {boolean} 是否成功点击
 */
function safeClick(selectors, elementName = '未知元素') {
    try {
        const element = safeQuerySelector(selectors);
        if (element) {
            // 确保元素可见和可点击
            if (element.offsetParent === null) {
                console.warn(`[人机验证处理器] 元素不可见: ${elementName}`);
                return false;
            }

            // 尝试多种点击方式
            try {
                element.click();
                console.log(`[人机验证处理器] ✅ 成功点击${elementName}`);
                return true;
            } catch (clickError) {
                // 如果普通点击失败，尝试触发事件
                console.warn(`[人机验证处理器] 普通点击失败，尝试事件触发: ${elementName}`);
                const event = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true
                });
                element.dispatchEvent(event);
                console.log(`[人机验证处理器] ✅ 通过事件成功点击${elementName}`);
                return true;
            }
        } else {
            console.warn(`[人机验证处理器] ❌ 未找到${elementName}`);
            return false;
        }
    } catch (error) {
        console.error(`[人机验证处理器] ❌ 点击${elementName}失败:`, error);
        return false;
    }
}

/**
 * 检测是否进入人机验证页面
 * @returns {boolean} 是否检测到人机验证页面
 */
function detectVerificationPage() {
    // 方法1: 检测原始的h1元素
    const h1Element = safeQuerySelector(CONFIG.VERIFICATION_SELECTOR);
    if (h1Element) {
        const text = h1Element.textContent.trim();
        console.log(`[人机验证处理器] 检测到H1元素文本: "${text}"`);

        if (text === CONFIG.VERIFICATION_TEXT) {
            console.log('[人机验证处理器] ✅ 通过H1元素确认进入人机验证页面');
            return true;
        }
    }

    // 方法2: 检测可访问性挑战按钮的存在
    const accessibilityButton = safeQuerySelector([
        CONFIG.FIRST_CLICK_SELECTOR,
        CONFIG.FIRST_CLICK_BACKUP_SELECTOR
    ]);

    if (accessibilityButton) {
        console.log('[人机验证处理器] ✅ 检测到可访问性挑战按钮，确认进入人机验证页面');
        return true;
    }

    // 方法3: 检测页面中是否包含人机验证相关文本
    const bodyText = document.body.textContent;
    if (bodyText.includes('可访问性挑战') || bodyText.includes('Human Challenge') ||
        bodyText.includes('人类挑战需要验证') || bodyText.includes('按住')) {
        console.log('[人机验证处理器] ✅ 通过页面文本确认进入人机验证页面');
        return true;
    }

    return false;
}

/**
 * 执行人机验证自动处理流程
 */
async function processVerification() {
    if (isProcessing) {
        console.log('[人机验证处理器] 已在处理中，跳过');
        return;
    }
    
    isProcessing = true;
    console.log('[人机验证处理器] 🚀 开始执行人机验证处理流程');
    
    try {
        // 步骤1: 点击第一个元素（可访问性挑战按钮）
        console.log('[人机验证处理器] 步骤1: 寻找并点击可访问性挑战按钮');
        const firstClickSuccess = safeClick([
            CONFIG.FIRST_CLICK_SELECTOR,
            CONFIG.FIRST_CLICK_BACKUP_SELECTOR,
            'a[role="button"]', // 更通用的选择器
            'a[tabindex="0"]:has(svg)' // 包含SVG的链接
        ], '可访问性挑战按钮');

        if (!firstClickSuccess) {
            console.error('[人机验证处理器] ❌ 可访问性挑战按钮点击失败，尝试查找所有可能的按钮');
            // 尝试查找页面上所有可能的按钮
            const allButtons = document.querySelectorAll('a[role="button"], button, [onclick], [tabindex="0"]');
            console.log(`[人机验证处理器] 页面上找到 ${allButtons.length} 个可能的按钮元素`);
            for (let i = 0; i < allButtons.length; i++) {
                const btn = allButtons[i];
                console.log(`[人机验证处理器] 按钮 ${i+1}: ${btn.tagName}, aria-label: "${btn.getAttribute('aria-label')}", text: "${btn.textContent.trim().substring(0, 50)}"`);
            }
            return;
        }

        // 步骤2: 等待12秒
        console.log(`[人机验证处理器] 步骤2: 等待 ${CONFIG.WAIT_TIME / 1000} 秒...`);
        let countdown = CONFIG.WAIT_TIME / 1000;
        const countdownInterval = setInterval(() => {
            console.log(`[人机验证处理器] ⏳ 倒计时: ${countdown} 秒`);
            countdown--;
            if (countdown <= 0) {
                clearInterval(countdownInterval);
            }
        }, 1000);

        await sleep(CONFIG.WAIT_TIME);

        // 步骤3: 点击第二个元素（按住按钮）
        console.log('[人机验证处理器] 步骤3: 寻找并点击按住按钮');
        const secondClickSuccess = safeClick([
            CONFIG.SECOND_CLICK_SELECTOR,
            CONFIG.SECOND_CLICK_BACKUP_SELECTOR,
            'div[role="button"]', // 更通用的选择器
            '.tiZTkgevcrkBKyp' // 通过class定位
        ], '按住按钮');

        if (secondClickSuccess) {
            console.log('[人机验证处理器] ✅ 人机验证处理流程完成');
        } else {
            console.error('[人机验证处理器] ❌ 按住按钮点击失败，尝试查找所有div按钮');
            const allDivButtons = document.querySelectorAll('div[role="button"], div[tabindex="0"], div[onclick]');
            console.log(`[人机验证处理器] 页面上找到 ${allDivButtons.length} 个div按钮元素`);
            for (let i = 0; i < allDivButtons.length; i++) {
                const btn = allDivButtons[i];
                console.log(`[人机验证处理器] Div按钮 ${i+1}: aria-label: "${btn.getAttribute('aria-label')}", class: "${btn.className}", text: "${btn.textContent.trim().substring(0, 50)}"`);
            }
        }

    } catch (error) {
        console.error('[人机验证处理器] ❌ 处理流程出错:', error);
    } finally {
        isProcessing = false;
    }
}

/**
 * 定期检查是否需要处理人机验证
 */
function startMonitoring() {
    console.log('[人机验证处理器] 开始监控页面...');
    
    const checkInterval = setInterval(() => {
        checkCount++;
        
        // 检查是否超过最大等待时间
        if (checkCount * CONFIG.CHECK_INTERVAL > CONFIG.MAX_WAIT_TIME) {
            console.log('[人机验证处理器] 监控超时，停止检查');
            clearInterval(checkInterval);
            return;
        }
        
        // 检查当前URL是否匹配
        if (!window.location.href.includes(CONFIG.TARGET_URL_PATTERN)) {
            console.log('[人机验证处理器] URL不匹配，停止监控');
            clearInterval(checkInterval);
            return;
        }
        
        // 检测人机验证页面
        if (detectVerificationPage()) {
            clearInterval(checkInterval);
            processVerification();
        }
        
    }, CONFIG.CHECK_INTERVAL);
}

// 页面加载完成后开始监控
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startMonitoring);
} else {
    startMonitoring();
}

// 监听页面变化（SPA应用可能需要）
const observer = new MutationObserver((mutations) => {
    if (!isProcessing && detectVerificationPage()) {
        processVerification();
    }
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});

console.log('[人机验证处理器] 初始化完成');
