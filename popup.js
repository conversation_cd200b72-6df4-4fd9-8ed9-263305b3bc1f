// Popup脚本
document.addEventListener('DOMContentLoaded', function() {
    const statusElement = document.getElementById('status');
    const openConsoleButton = document.getElementById('openConsole');
    
    // 检查当前标签页是否为目标网页
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        const currentTab = tabs[0];
        const isTargetPage = currentTab.url && currentTab.url.includes('signup.live.com');
        
        if (isTargetPage) {
            statusElement.className = 'status active';
            statusElement.innerHTML = '<strong>状态：已激活</strong><br><small>正在监控当前页面</small>';
        } else {
            statusElement.className = 'status inactive';
            statusElement.innerHTML = '<strong>状态：未激活</strong><br><small>请访问目标网页</small>';
        }
    });
    
    // 打开开发者控制台按钮
    openConsoleButton.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.scripting.executeScript({
                target: {tabId: tabs[0].id},
                func: function() {
                    // 提示用户手动打开控制台
                    console.log('%c[人机验证处理器] 请按F12打开开发者工具查看详细日志', 'color: #007bff; font-size: 14px; font-weight: bold;');
                    alert('请按F12键打开开发者工具，然后查看Console标签页的日志输出');
                }
            });
        });
    });
});
