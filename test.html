<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人机验证测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .verification-section {
            text-align: center;
            margin: 40px 0;
        }
        .verification-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 30px;
        }
        .click-button {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .click-button:hover {
            background-color: #0056b3;
        }
        .click-button.clicked {
            background-color: #28a745;
        }
        .log-section {
            margin-top: 40px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .log-title {
            font-size: 18px;
            margin-bottom: 15px;
            color: #333;
        }
        .log-content {
            font-family: monospace;
            font-size: 14px;
            line-height: 1.5;
            color: #666;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: white;
        }
        .instructions {
            background-color: #e7f3ff;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .instructions h3 {
            color: #0066cc;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Chrome扩展测试页面</h1>
        
        <div class="instructions">
            <h3>测试说明</h3>
            <p>这是一个用于测试人机验证自动处理器Chrome扩展的页面。</p>
            <ol>
                <li>确保已安装并启用Chrome扩展</li>
                <li>打开开发者工具（F12）查看控制台日志</li>
                <li>页面会模拟人机验证的元素结构</li>
                <li>扩展应该自动检测并执行点击流程</li>
            </ol>
        </div>

        <!-- 模拟目标网页的结构 -->
        <div id="view">
            <div>
                <div>
                    <div>
                        <div class="verification-section">
                            <h1 class="verification-title">证明你不是机器人</h1>
                            
                            <button id="xpplOSafccGevzl" class="click-button">
                                第一个按钮 (将被自动点击)
                            </button>
                            
                            <button id="WKEHFCDuGlNZPmJ" class="click-button">
                                第二个按钮 (12秒后被点击)
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="log-section">
            <div class="log-title">页面日志</div>
            <div id="logContent" class="log-content">
                等待扩展执行...<br>
            </div>
        </div>
    </div>

    <script>
        // 页面日志功能
        const logContent = document.getElementById('logContent');
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += `[${timestamp}] ${message}<br>`;
            logContent.scrollTop = logContent.scrollHeight;
        }

        // 监听按钮点击
        document.getElementById('xpplOSafccGevzl').addEventListener('click', function() {
            this.classList.add('clicked');
            this.textContent = '第一个按钮 (已点击)';
            addLog('✅ 第一个按钮被点击');
        });

        document.getElementById('WKEHFCDuGlNZPmJ').addEventListener('click', function() {
            this.classList.add('clicked');
            this.textContent = '第二个按钮 (已点击)';
            addLog('✅ 第二个按钮被点击');
            addLog('🎉 人机验证流程完成！');
        });

        // 页面加载完成
        addLog('📄 测试页面已加载');
        addLog('🔍 等待扩展检测人机验证页面...');

        // 模拟扩展可能遇到的一些情况
        setTimeout(() => {
            addLog('⏱️ 页面已稳定，扩展应该开始工作');
        }, 2000);
    </script>
</body>
</html>
