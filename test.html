<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人机验证测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .verification-section {
            text-align: center;
            margin: 40px 0;
        }
        .verification-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 30px;
        }
        .click-button {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .click-button:hover {
            background-color: #0056b3;
        }
        .click-button.clicked {
            background-color: #28a745;
        }
        .log-section {
            margin-top: 40px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .log-title {
            font-size: 18px;
            margin-bottom: 15px;
            color: #333;
        }
        .log-content {
            font-family: monospace;
            font-size: 14px;
            line-height: 1.5;
            color: #666;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: white;
        }
        .instructions {
            background-color: #e7f3ff;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .instructions h3 {
            color: #0066cc;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Chrome扩展测试页面</h1>
        
        <div class="instructions">
            <h3>测试说明</h3>
            <p>这是一个用于测试人机验证自动处理器Chrome扩展的页面。</p>
            <ol>
                <li>确保已安装并启用Chrome扩展</li>
                <li>打开开发者工具（F12）查看控制台日志</li>
                <li>页面会模拟人机验证的元素结构</li>
                <li>扩展应该自动检测并执行点击流程</li>
            </ol>
        </div>

        <!-- 模拟目标网页的结构 -->
        <div id="view">
            <div>
                <div>
                    <div>
                        <div class="verification-section">
                            <h1 class="verification-title">证明你不是机器人</h1>

                            <!-- 模拟可访问性挑战按钮 -->
                            <div style="text-align: center; margin: 20px 0;">
                                <a style="cursor:pointer; display: inline-block;width:58px; position: relative; vertical-align: middle;"
                                   tabindex="0"
                                   id="testAccessibilityButton"
                                   role="button"
                                   aria-label="可访问性挑战"
                                   aria-describedby="testAccessibilityDesc">
                                    <svg width="50" height="40" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <g filter="url(#filter0_d_1_9)">
                                            <path d="M25 44C36.0457 44 45 35.0457 45 24C45 12.9543 36.0457 4 25 4C13.9543 4 5 12.9543 5 24C5 35.0457 13.9543 44 25 44Z" fill="#FDFDFF"></path>
                                            <path d="M25 44C36.0457 44 45 35.0457 45 24C45 12.9543 36.0457 4 25 4C13.9543 4 5 12.9543 5 24C5 35.0457 13.9543 44 25 44Z" fill="#F7F8FA"></path>
                                            <path d="M25 44C36.0457 44 45 35.0457 45 24C45 12.9543 36.0457 4 25 4C13.9543 4 5 12.9543 5 24C5 35.0457 13.9543 44 25 44Z" fill="white"></path>
                                            <path d="M45 24C45 12.997 36.057 4 25 4C13.943 4 5 12.997 5 24C5 35.003 13.997 44 25 44C36.003 44 45 35.003 45 24ZM25 10.07C25.3988 10.0693 25.7939 10.1474 26.1625 10.2998C26.531 10.4521 26.8659 10.6758 27.1479 10.9578C27.4299 11.2399 27.6534 11.5748 27.8056 11.9435C27.9578 12.3121 28.0358 12.7072 28.035 13.106C28.0355 13.5047 27.9574 13.8995 27.805 14.2679C27.6527 14.6363 27.4291 14.971 27.1472 15.2529C26.8652 15.5347 26.5304 15.7582 26.162 15.9104C25.7935 16.0626 25.3987 16.1407 25 16.14C24.6013 16.1407 24.2063 16.0626 23.8378 15.9103C23.4693 15.758 23.1344 15.5345 22.8525 15.2525C22.5705 14.9706 22.347 14.6357 22.1947 14.2672C22.0424 13.8987 21.9643 13.5037 21.965 13.105C21.965 11.48 23.32 10.07 25 10.07ZM21.965 36.575C21.8466 36.8501 21.6496 37.0841 21.3988 37.2477C21.148 37.4114 20.8545 37.4974 20.555 37.495C20.339 37.495 20.122 37.442 19.905 37.333C19.092 36.953 18.767 36.033 19.146 35.22C19.146 35.22 22.127 28.39 22.669 25.897C22.886 25.03 22.995 22.699 23.049 21.615C23.049 21.235 22.832 20.911 22.507 20.802L15.786 18.851C14.919 18.58 14.431 17.659 14.702 16.846C14.972 16.033 15.894 15.653 16.707 15.87C16.707 15.87 22.832 17.821 25 17.821C27.168 17.821 33.401 15.816 33.401 15.816C34.214 15.599 35.136 16.086 35.352 16.9C35.569 17.713 35.082 18.634 34.268 18.85L27.602 20.856C27.276 20.965 27.005 21.29 27.06 21.669C27.114 22.753 27.222 25.084 27.439 25.951C27.981 28.444 30.962 35.274 30.962 35.274C31.342 36.087 30.962 37.008 30.203 37.388C30.002 37.4922 29.7794 37.5477 29.553 37.55C28.957 37.55 28.36 37.225 28.143 36.629L25 30.07L21.965 36.575Z" fill="#424257"></path>
                                        </g>
                                        <defs>
                                            <filter id="filter0_d_1_9" x="0" y="0" width="50" height="50" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                                <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
                                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix>
                                                <feOffset dy="1"></feOffset>
                                                <feGaussianBlur stdDeviation="2.5"></feGaussianBlur>
                                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.206358 0"></feColorMatrix>
                                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_9"></feBlend>
                                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_9" result="shape"></feBlend>
                                            </filter>
                                        </defs>
                                    </svg>
                                    <div id="testAccessibilityText">可访问性挑战</div>
                                    <span id="testAccessibilityDesc" style="position: absolute; clip: rect(0 0 0 0); border: 0; width: 1px; height: 1px; margin: -1px; overflow: hidden; padding: 0;">人类挑战需要验证。请按一次按钮，等待确认，并在出现提示时再按一次。</span>
                                </a>
                            </div>

                            <!-- 模拟按住按钮 -->
                            <div style="text-align: center; margin: 20px 0;">
                                <div id="testHoldButton"
                                     class="tiZTkgevcrkBKyp click-button"
                                     tabindex="0"
                                     aria-describedby="testHoldDesc"
                                     role="button"
                                     aria-label="按住 人工挑战">
                                    <div id="testHoldInner"></div>
                                    <div id="testHoldContent" dir="auto">
                                        <div id="testHoldIcon"></div>
                                        <div id="testHoldTextContainer">
                                            <p id="testHoldMainText" class="VsfdnhoxbcYUyXd">按住</p>
                                            <span id="testHoldDesc" class="ysmRhpZvBjGNuCA">Human Challenge需要验证。请按住按钮直到验证完成</span>
                                            <span id="testHoldLive" class="ysmRhpZvBjGNuCA" aria-live="assertive"></span>
                                        </div>
                                        <div class="fetching-volume"><span>•</span><span>•</span><span>•</span></div>
                                        <div id="checkmark"></div>
                                        <div id="ripple"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="log-section">
            <div class="log-title">页面日志</div>
            <div id="logContent" class="log-content">
                等待扩展执行...<br>
            </div>
        </div>
    </div>

    <script>
        // 页面日志功能
        const logContent = document.getElementById('logContent');
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += `[${timestamp}] ${message}<br>`;
            logContent.scrollTop = logContent.scrollHeight;
        }

        // 监听按钮点击
        document.getElementById('testAccessibilityButton').addEventListener('click', function() {
            this.style.backgroundColor = '#28a745';
            this.style.color = 'white';
            this.style.padding = '10px';
            this.style.borderRadius = '5px';
            const textDiv = this.querySelector('div');
            if (textDiv) textDiv.textContent = '可访问性挑战 (已点击)';
            addLog('✅ 可访问性挑战按钮被点击');
        });

        document.getElementById('testHoldButton').addEventListener('click', function() {
            this.classList.add('clicked');
            this.style.backgroundColor = '#28a745';
            const mainText = this.querySelector('#testHoldMainText');
            if (mainText) mainText.textContent = '按住 (已点击)';
            addLog('✅ 按住按钮被点击');
            addLog('🎉 人机验证流程完成！');
        });

        // 页面加载完成
        addLog('📄 测试页面已加载');
        addLog('🔍 等待扩展检测人机验证页面...');

        // 模拟扩展可能遇到的一些情况
        setTimeout(() => {
            addLog('⏱️ 页面已稳定，扩展应该开始工作');
        }, 2000);
    </script>
</body>
</html>
