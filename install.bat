@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    人机验证自动处理器 Chrome扩展
echo ========================================
echo.
echo 正在检查文件...
echo.

REM 检查必要文件是否存在
set "files_missing=0"

if not exist "manifest.json" (
    echo ❌ manifest.json 文件缺失
    set "files_missing=1"
) else (
    echo ✅ manifest.json 文件存在
)

if not exist "content.js" (
    echo ❌ content.js 文件缺失
    set "files_missing=1"
) else (
    echo ✅ content.js 文件存在
)

if not exist "popup.html" (
    echo ❌ popup.html 文件缺失
    set "files_missing=1"
) else (
    echo ✅ popup.html 文件存在
)

if not exist "popup.js" (
    echo ❌ popup.js 文件缺失
    set "files_missing=1"
) else (
    echo ✅ popup.js 文件存在
)

echo.

if "%files_missing%"=="1" (
    echo ❌ 有文件缺失，请确保所有必要文件都在当前目录中
    echo.
    pause
    exit /b 1
)

echo ✅ 所有必要文件都已准备就绪！
echo.
echo 📋 安装步骤：
echo.
echo 1. 打开Chrome浏览器
echo 2. 在地址栏输入: chrome://extensions/
echo 3. 在右上角开启"开发者模式"
echo 4. 点击"加载已解压的扩展程序"
echo 5. 选择当前文件夹: %CD%
echo 6. 扩展安装完成！
echo.
echo 🧪 测试步骤：
echo.
echo 1. 在浏览器中打开 test.html 文件进行测试
echo 2. 按F12打开开发者工具查看控制台日志
echo 3. 访问目标网页进行实际测试
echo.
echo 📖 更多信息请查看 README.md 和 INSTALL.md 文件
echo.

REM 询问是否打开Chrome扩展页面
set /p "open_chrome=是否现在打开Chrome扩展管理页面？(y/n): "
if /i "%open_chrome%"=="y" (
    echo.
    echo 正在打开Chrome扩展管理页面...
    start chrome://extensions/
)

REM 询问是否打开测试页面
set /p "open_test=是否现在打开测试页面？(y/n): "
if /i "%open_test%"=="y" (
    echo.
    echo 正在打开测试页面...
    start test.html
)

echo.
echo 安装准备完成！
echo.
pause
