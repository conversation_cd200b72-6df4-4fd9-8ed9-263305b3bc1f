# Chrome扩展安装和使用指南

## 📦 安装步骤

### 1. 准备文件
确保你有以下文件：
- `manifest.json` - 扩展配置文件
- `content.js` - 主要功能脚本
- `popup.html` - 扩展弹出页面
- `popup.js` - 弹出页面脚本

### 2. 安装扩展
1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 在右上角开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含所有文件的文件夹
6. 扩展安装完成！

### 3. 验证安装
- 在Chrome工具栏应该能看到扩展图标
- 点击图标可以打开扩展弹出页面

## 🧪 测试扩展

### 方法1：使用测试页面
1. 在浏览器中打开 `test.html` 文件
2. 按F12打开开发者工具
3. 查看Console标签页的日志输出
4. 观察页面上的按钮是否被自动点击

### 方法2：在实际网页测试
1. 访问目标网页：
   ```
   https://signup.live.com/signup?sru=https%3a%2f%2flogin.live.com%2foauth20_authorize.srf%3flc%3d2052%26client_id%3d9199bf20-a13f-4107-85dc-02114787ef48%26cobrandid%3dab0455a0-8d03-46b9-b18b-df2f57b9e44c%26mkt%3dZH-CN%26opid%3dA00C59CDBEE90B9F%26opidt%3d1753684045%26uaid%3dd0a70ae63a65372d46095767ac77d3b4%26contextid%3d421E0E92D273B7E9%26opignore%3d1&mkt=ZH-CN&uiflavor=web&fl=dob%2cflname%2cwld&cobrandid=ab0455a0-8d03-46b9-b18b-df2f57b9e44c&client_id=9199bf20-a13f-4107-85dc-02114787ef48&uaid=d0a70ae63a65372d46095767ac77d3b4&suc=9199bf20-a13f-4107-85dc-02114787ef48&fluent=2&lic=1
   ```
2. 按F12打开开发者工具
3. 等待人机验证页面出现
4. 观察扩展是否自动处理

## 📋 使用说明

### 扩展工作流程
1. **页面监控**：扩展会持续监控页面变化
2. **验证检测**：当检测到包含"证明你不是机器人"文本的特定元素时
3. **自动点击**：按以下顺序执行：
   - 点击 `#xpplOSafccGevzl` 元素
   - 等待12秒
   - 点击 `#WKEHFCDuGlNZPmJ` 元素

### 查看日志
- 按F12打开开发者工具
- 切换到"Console"标签页
- 查看以 `[人机验证处理器]` 开头的日志信息

### 日志说明
- `🚀` 表示流程开始
- `✅` 表示操作成功
- `⚠️` 表示警告信息
- `❌` 表示错误信息

## 🔧 故障排除

### 扩展不工作
1. 确认扩展已正确安装并启用
2. 检查是否在正确的网页上（signup.live.com）
3. 刷新页面重试
4. 查看控制台是否有错误信息

### 元素未找到
1. 检查网页是否完全加载
2. 确认目标元素的选择器是否正确
3. 网页结构可能已更改，需要更新选择器

### 点击无效
1. 确认元素是否可见和可点击
2. 检查是否有其他脚本干扰
3. 尝试手动点击验证元素是否正常

## ⚙️ 自定义配置

如需修改扩展行为，可以编辑 `content.js` 中的 `CONFIG` 对象：

```javascript
const CONFIG = {
    VERIFICATION_TEXT: '证明你不是机器人',  // 检测文本
    FIRST_CLICK_SELECTOR: '#xpplOSafccGevzl',  // 第一个按钮
    SECOND_CLICK_SELECTOR: '#WKEHFCDuGlNZPmJ', // 第二个按钮
    WAIT_TIME: 12000,  // 等待时间（毫秒）
    CHECK_INTERVAL: 1000,  // 检查间隔
    MAX_WAIT_TIME: 30000   // 最大等待时间
};
```

修改后需要重新加载扩展：
1. 进入 `chrome://extensions/`
2. 找到扩展，点击刷新按钮
3. 重新访问目标网页测试

## 🛡️ 安全提醒

- 此扩展仅在指定域名下工作
- 不会收集或传输任何个人数据
- 所有操作都在本地浏览器中完成
- 建议仅在需要时启用扩展
