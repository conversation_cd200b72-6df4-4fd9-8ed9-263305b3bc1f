# 人机验证自动处理器 Chrome扩展

这是一个专门为处理特定网页人机验证而设计的Chrome扩展。

## 功能特性

- 🎯 **精确检测**：自动识别人机验证页面（通过检测"证明你不是机器人"文本）
- 🤖 **自动处理**：按照预设流程自动点击相应元素
- ⏱️ **智能等待**：在操作间隔中等待适当时间
- 📝 **详细日志**：在控制台输出详细的执行日志，便于调试和监控

## 安装方法

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入扩展管理页面（chrome://extensions/）
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹

## 使用方法

1. 安装扩展后，访问目标网页：
   ```
   https://signup.live.com/signup?...
   ```

2. 扩展会自动监控页面变化

3. 当检测到人机验证页面时，会自动执行以下流程：
   - 检测页面元素 `#view > div > div:nth-child(2) > div > h1` 中的"证明你不是机器人"文本
   - 点击元素 `#xpplOSafccGevzl`
   - 等待12秒
   - 点击元素 `#WKEHFCDuGlNZPmJ`

4. 打开开发者工具（F12）查看控制台日志，了解执行进度

## 文件结构

```
├── manifest.json      # 扩展配置文件
├── content.js         # 内容脚本（主要逻辑）
├── popup.html         # 弹出页面HTML
├── popup.js           # 弹出页面脚本
└── README.md          # 说明文档
```

## 配置参数

在 `content.js` 中的 `CONFIG` 对象可以调整以下参数：

- `VERIFICATION_TEXT`: 检测的文本内容
- `FIRST_CLICK_SELECTOR`: 第一个点击元素的选择器
- `SECOND_CLICK_SELECTOR`: 第二个点击元素的选择器
- `WAIT_TIME`: 两次点击间的等待时间（毫秒）
- `CHECK_INTERVAL`: 页面检查间隔（毫秒）
- `MAX_WAIT_TIME`: 最大等待时间（毫秒）

## 日志输出

扩展会在控制台输出详细的执行日志，包括：

- ✅ 成功操作的确认信息
- ⚠️ 警告信息（如元素未找到）
- ❌ 错误信息
- 🚀 流程开始和完成状态

## 注意事项

- 此扩展仅在指定的目标网页上工作
- 请确保网页完全加载后再进行操作
- 如遇到问题，请查看控制台日志进行调试
- 扩展会自动停止监控超时或URL不匹配的情况

## 安全说明

- 扩展仅在指定域名下运行
- 不会收集或传输任何用户数据
- 所有操作都在本地浏览器中完成
